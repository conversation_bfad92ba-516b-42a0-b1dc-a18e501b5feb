
// CthulhuJS Anti-Fingerprint Basic Protection
(function() {
    'use strict';
    
    console.log('🐙 CthulhuJS Anti-Fingerprint Basic Protection loaded');
    
    // Basic canvas fingerprinting protection
    const originalGetContext = HTMLCanvasElement.prototype.getContext;
    HTMLCanvasElement.prototype.getContext = function(type, ...args) {
        const context = originalGetContext.apply(this, [type, ...args]);
        
        if (type === '2d' && context) {
            const originalGetImageData = context.getImageData;
            context.getImageData = function(...args) {
                const imageData = originalGetImageData.apply(this, args);
                // Add slight noise to prevent fingerprinting
                for (let i = 0; i < imageData.data.length; i += 4) {
                    imageData.data[i] += Math.floor(Math.random() * 3) - 1;
                    imageData.data[i + 1] += Math.floor(Math.random() * 3) - 1;
                    imageData.data[i + 2] += Math.floor(Math.random() * 3) - 1;
                }
                return imageData;
            };
        }
        
        return context;
    };
    
    // Basic WebGL fingerprinting protection
    const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === this.RENDERER || parameter === this.VENDOR) {
            return 'Generic Renderer';
        }
        return originalGetParameter.apply(this, arguments);
    };
    
})();
