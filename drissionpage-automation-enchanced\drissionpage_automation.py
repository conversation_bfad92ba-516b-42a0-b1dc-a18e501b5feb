import time
import json
import re
import sys
import os
import platform
import socket
from pathlib import Path

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

from DrissionPage import ChromiumPage, ChromiumOptions
from config import DrissionPageConfig
from drissionpage_logger import DrissionPageLogger

# Import handlers
from handlers import CaptchaHandler, AugmentAuth, TokenStorage, OneMailHandler
from fingerprint_defender_plugin import FingerprintDefenderPlugin
from advanced_fingerprint_protection import AdvancedFingerprintProtection
from canvas_randomizer import CanvasRandomizer

def find_chrome_path():
    """
    动态查找 Chrome 浏览器路径，优先支持 Ubuntu 环境
    """
    # Ubuntu/Linux 常见路径（按优先级排序）
    linux_paths = [
        '/usr/bin/google-chrome',
        '/opt/google/chrome/google-chrome',
        '/usr/bin/google-chrome-stable',
        '/usr/bin/chromium-browser',
        '/snap/bin/chromium',
        '/usr/bin/chromium',
        '/opt/google/chrome/chrome'
    ]

    # Windows 路径
    windows_paths = [
        r'C:\Program Files\Google\Chrome\Application\chrome.exe',
        r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
        r'C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe'.format(os.getenv('USERNAME', '')),
    ]

    # macOS 路径
    macos_paths = [
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        '/Applications/Chromium.app/Contents/MacOS/Chromium'
    ]

    # 根据操作系统选择路径列表
    system = platform.system().lower()
    if system == 'linux':
        paths_to_check = linux_paths
    elif system == 'windows':
        paths_to_check = windows_paths
    elif system == 'darwin':
        paths_to_check = macos_paths
    else:
        paths_to_check = linux_paths + windows_paths + macos_paths

    # 检查路径是否存在
    for path in paths_to_check:
        if os.path.exists(path) and os.access(path, os.X_OK):
            return path

    return None

def find_available_port(start_port=9222, max_attempts=50):
    """
    查找可用的端口，避免端口冲突
    """
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue

    # 如果都不可用，返回一个随机端口
    import random
    return random.randint(9300, 9999)

class DrissionPageAutomation:
    """
    DrissionPage Automation
    使用 DrissionPage 进行真实浏览器自动化
    """

    def __init__(self):
        self.config = DrissionPageConfig()
        self.logger = DrissionPageLogger()

        self.page = None
        self.temp_email = None
        self.virtual_display = None  # 虚拟显示器

        # 复用现有的处理器
        self.captcha_handler = CaptchaHandler()
        self.onemail_handler = OneMailHandler()
        self.augment_auth = AugmentAuth()
        self.token_storage = TokenStorage()

        # 指纹防护插件管理器（延迟初始化）
        self.fingerprint_defender_plugin = None

        # 高级指纹防护器（延迟初始化）
        self.advanced_fingerprint_protection = None

        # Canvas 随机化器（延迟初始化）
        self.canvas_randomizer = None

        # 在 Linux 环境下自动启动虚拟显示器
        self._setup_virtual_display()

        self.logger.log('🤖 DrissionPage Automation 初始化完成')

    def _setup_virtual_display(self):
        """设置虚拟显示器（仅在 Linux 环境下）"""
        if platform.system().lower() != 'linux':
            return

        try:
            from pyvirtualdisplay import Display

            # 检查是否已有 DISPLAY 环境变量
            if os.environ.get('DISPLAY'):
                self.logger.log(f'🖥️ 检测到现有显示器: {os.environ.get("DISPLAY")}')
                return

            # 创建虚拟显示器
            self.virtual_display = Display(
                visible=False,  # 不可见
                size=(1920, 1080),  # 高分辨率
                backend='xvfb'  # 使用 Xvfb 后端
            )
            self.virtual_display.start()

            self.logger.log(f'🖥️ 虚拟显示器已启动: {self.virtual_display.display}')
            self.logger.log('💡 这将提高在无显示器服务器上的成功率')

        except ImportError:
            self.logger.log('⚠️ PyVirtualDisplay 未安装，建议安装以提高成功率:')
            self.logger.log('   pip install PyVirtualDisplay')
        except Exception as e:
            self.logger.log(f'⚠️ 虚拟显示器启动失败: {e}')
            self.logger.log('💡 可能需要安装 xvfb: sudo apt install xvfb')

    def _cleanup_virtual_display(self):
        """清理虚拟显示器"""
        if self.virtual_display:
            try:
                self.virtual_display.stop()
                self.logger.log('🖥️ 虚拟显示器已停止')
            except Exception as e:
                self.logger.log(f'⚠️ 虚拟显示器停止失败: {e}')

    def init_browser(self):
        """初始化浏览器"""
        self.logger.log('🚀 启动 DrissionPage 浏览器...')
        self.config.print_config()

        try:
            # 创建浏览器选项 - 使用正确的 DrissionPage API
            options = ChromiumOptions()

            # 预先收集并添加所有扩展
            self.logger.log('🧩 预加载扩展目录...')
            try:
                from pathlib import Path as _Path
                ext_paths = []

                # 检查 CthulhuJS 插件
                cthulhujs_dir = _Path(__file__).parent / 'cthulhujs_anti_fingerprint'
                if cthulhujs_dir.exists():
                    ext_paths.append(str(cthulhujs_dir))
                    self.logger.log(f'🐙 预加载 CthulhuJS: {cthulhujs_dir}')

                # 检查其他插件
                for _name in ['canvas_fingerprint_defender', 'proxy_auth_extension']:
                    _d = _Path(__file__).parent / _name
                    if _d.exists():
                        ext_paths.append(str(_d))
                        self.logger.log(f'🧩 预加载扩展: {_d}')

                # 使用 add_extension 方法逐个添加
                for ext_path in ext_paths:
                    try:
                        options.add_extension(ext_path)
                        self.logger.log(f'✅ 已添加扩展: {ext_path}')
                    except Exception as e:
                        self.logger.log(f'⚠️ 添加扩展失败 {ext_path}: {e}')

                if ext_paths:
                    self.logger.log(f'🎯 总共预加载了 {len(ext_paths)} 个扩展')
                else:
                    self.logger.log('⚠️ 未发现任何扩展目录进行预加载')

            except Exception as e:
                self.logger.log(f'⚠️ 扩展预加载失败: {e}')

            # Ubuntu/Linux 兼容性：动态设置 Chrome 路径
            chrome_path = find_chrome_path()
            if chrome_path:
                self.logger.log(f'🔍 检测到 Chrome 路径: {chrome_path}')
                options.set_paths(browser_path=chrome_path)
            else:
                self.logger.log('⚠️ 未找到 Chrome 路径，使用默认设置')

            # 动态分配可用端口，避免冲突
            available_port = find_available_port()
            self.debug_port = available_port
            self.logger.log(f'🔌 使用调试端口: {available_port}')

            # 基础选项 - 使用 set_argument 方法
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-setuid-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-accelerated-2d-canvas')
            options.set_argument('--no-first-run')
            options.set_argument('--no-zygote')
            options.set_argument('--disable-gpu')
            options.set_argument('--disable-background-timer-throttling')
            options.set_argument('--disable-backgrounding-occluded-windows')
            options.set_argument('--disable-renderer-backgrounding')

            # 用户指定的增强参数
            options.set_argument('--force-color-profile=srgb')
            options.set_argument('--metrics-recording-only')
            options.set_argument('--password-store=basic')
            options.set_argument('--use-mock-keychain')
            options.set_argument('--export-tagged-pdf')
            options.set_argument('--no-default-browser-check')
            options.set_argument('--disable-background-mode')
            options.set_argument('--enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions')
            options.set_argument('--disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage')
            options.set_argument('--deny-permission-prompts')
            options.set_argument('--accept-lang=en-US')

            # 根据配置决定是否显示浏览器界面
            if self.config.headfull:
                self.logger.log('🖥️ 使用有界面模式 - 将显示浏览器窗口')

                # Ubuntu GUI模式特殊配置
                options.set_argument('--disable-web-security')
                options.set_argument('--disable-features=VizDisplayCompositor')
                options.set_argument('--disable-ipc-flooding-protection')
                options.set_argument('--disable-infobars')
                # 注释掉 --disable-extensions 以支持插件加载
                # options.set_argument('--disable-extensions')
                options.set_argument('--start-maximized')
                options.set_argument('--window-size=1920,1080')

                # 设置独立的用户数据目录避免冲突
                import tempfile
                import os
                user_data_dir = os.path.join(tempfile.gettempdir(), 'chrome-drissionpage-gui')
                os.makedirs(user_data_dir, exist_ok=True)
                options.set_argument(f'--user-data-dir={user_data_dir}')

                # 设置远程调试端口（使用动态分配的端口）
                options.set_argument(f'--remote-debugging-port={available_port}')

                # Ubuntu特有的显示相关参数
                options.set_argument('--disable-field-trial-config')

                self.logger.log(f'📁 用户数据目录: {user_data_dir}')
                self.logger.log(f'🔌 远程调试端口: {available_port}')

                # 设置本地端口（重要：与远程调试端口保持一致）
                options.set_local_port(available_port)
            else:
                self.logger.log('👻 使用无界面模式 (headless)')
                options.set_argument('--headless=new')
                options.set_argument('--window-size=1920,1080')

            # 增加稳定性参数（避免与上面重复）
            if not self.config.headfull:
                # 只在 headless 模式下添加这些参数，避免与 GUI 模式重复
                options.set_argument('--disable-web-security')
                options.set_argument('--disable-features=VizDisplayCompositor')
                options.set_argument('--disable-ipc-flooding-protection')

            options.set_argument('--disable-hang-monitor')
            options.set_argument('--disable-client-side-phishing-detection')
            options.set_argument('--disable-popup-blocking')
            options.set_argument('--disable-prompt-on-repost')
            options.set_argument('--disable-sync')
            options.set_argument('--disable-translate')
            options.set_argument('--disable-windows10-custom-titlebar')
            options.set_argument('--memory-pressure-off')
            options.set_argument('--max_old_space_size=4096')

            # 如果启用代理，使用扩展方式（参考网络最佳实践）
            if self.config.drissionpage_proxy:
                proxy_config = self.config.get_proxy_config()
                if proxy_config:
                    # 使用扩展方式设置代理（更稳定，支持认证）
                    try:
                        ext_path = self._create_proxy_auth_extension(
                            proxy_config['host'],
                            proxy_config['port'],
                            proxy_config['username'],
                            proxy_config['password']
                        )
                        options.add_extension(ext_path)
                        self.logger.log(f'🧩 已加载代理认证扩展: {proxy_config["username"]}@{proxy_config["host"]}:{proxy_config["port"]}')
                    except Exception as ee:
                        self.logger.log(f'⚠️ 代理认证扩展加载失败: {ee}')
                        # 回退到启动参数方式
                        if proxy_config['username'] and proxy_config['password']:
                            proxy_server = f"http://{proxy_config['username']}:{proxy_config['password']}@{proxy_config['host']}:{proxy_config['port']}"
                        else:
                            proxy_server = f"http://{proxy_config['host']}:{proxy_config['port']}"
                        options.set_argument(f'--proxy-server={proxy_server}')
                        self.logger.log(f'🌐 回退使用启动参数代理: {proxy_config["host"]}:{proxy_config["port"]}')

                    # 添加代理相关的Chrome参数来提高兼容性
                    options.set_argument('--ignore-certificate-errors')
                    options.set_argument('--ignore-ssl-errors')
                    options.set_argument('--allow-running-insecure-content')

            # 加载 FingerprintDefender 插件（如果启用）
            if self.config.fingerprint_protection:
                try:
                    # 创建临时的插件管理器来设置插件
                    temp_plugin_manager = FingerprintDefenderPlugin(self.logger)

                    # 检查是否有 Canvas Fingerprint Defender 插件文件
                    plugin_found = False

                    # 检查 .crx 文件
                    crx_path = Path(__file__).parent / 'canvas_fingerprint_defender.crx'
                    if crx_path.exists():
                        temp_plugin_manager.setup_plugin_directory(str(crx_path))
                        self.logger.log('🛡️ 使用 Canvas Fingerprint Defender (.crx 文件)')
                        plugin_found = True

                    # 检查解压后的文件夹
                    if not plugin_found:
                        folder_path = Path(__file__).parent / 'canvas_fingerprint_defender'
                        if folder_path.exists() and (folder_path / 'manifest.json').exists():
                            temp_plugin_manager.setup_plugin_directory(str(folder_path))
                            self.logger.log('🛡️ 使用 Canvas Fingerprint Defender (文件夹)')
                            plugin_found = True

                    # 如果没有找到真实插件，尝试自动下载
                    if not plugin_found:
                        self.logger.log('🔍 未找到 Canvas Fingerprint Defender，尝试自动下载...')
                        try:
                            from fingerprint_defender_plugin import auto_download_canvas_fingerprint_defender
                            download_result = auto_download_canvas_fingerprint_defender()

                            if download_result:
                                # 下载成功，重新设置插件
                                temp_plugin_manager.setup_plugin_directory(download_result)
                                self.logger.log('🎉 Canvas Fingerprint Defender 自动下载成功')
                                plugin_found = True
                            else:
                                self.logger.log('⚠️ 自动下载失败，使用基本指纹防护')
                        except Exception as e:
                            self.logger.log(f'⚠️ 自动下载出错: {e}，使用基本指纹防护')

                        # 如果自动下载也失败，使用基本防护
                        if not plugin_found:
                            temp_plugin_manager.setup_plugin_directory()
                            self.logger.log('🛡️ 使用基本指纹防护')
                            self.logger.log('💡 要使用真实插件，请运行: python fingerprint_defender_plugin.py')

                    # 验证并加载插件
                    is_valid, message = temp_plugin_manager.validate_plugin()
                    if is_valid:
                        plugin_path = temp_plugin_manager.get_plugin_path()
                        options.add_extension(plugin_path)
                        self.logger.log(f'🧩 已加载 FingerprintDefender 插件: {plugin_path}')
                    else:
                        self.logger.log(f'⚠️ FingerprintDefender 插件验证失败: {message}')

                except Exception as e:
                    self.logger.log(f'⚠️ FingerprintDefender 插件加载失败: {e}')
            else:
                self.logger.log('⚠️ 指纹防护已禁用 (DRISSON_FINGERPRINT_PROTECTION=false)')

            # 加载 CthulhuJS Anti-Fingerprint 插件
            if self.config.cthulhujs_plugin:
                try:
                    from cthulhujs_plugin import CthulhuJSPlugin, auto_download_cthulhujs_plugin

                    self.logger.log('🐙 正在加载 CthulhuJS Anti-Fingerprint 插件...')
                    temp_cthulhujs_manager = CthulhuJSPlugin(self.logger)

                    # 检查是否存在现有的 CRX 文件
                    crx_path = Path(__file__).parent / 'cthulhujs_anti_fingerprint.crx'
                    plugin_found = False

                    if crx_path.exists():
                        self.logger.log(f'📦 发现现有 CthulhuJS 插件文件: {crx_path}')
                        temp_cthulhujs_manager.setup_plugin_directory(str(crx_path))
                        plugin_found = True
                    else:
                        self.logger.log('📥 未发现 CthulhuJS 插件文件，尝试自动下载...')

                        try:
                            # 尝试自动下载
                            download_result = auto_download_cthulhujs_plugin()
                            if download_result:
                                # 下载成功，重新设置插件
                                temp_cthulhujs_manager.setup_plugin_directory(download_result)
                                self.logger.log('🎉 CthulhuJS Anti-Fingerprint 自动下载成功')
                                plugin_found = True
                            else:
                                self.logger.log('⚠️ 自动下载失败，使用基本反指纹防护')
                        except Exception as e:
                            self.logger.log(f'⚠️ 自动下载出错: {e}，使用基本反指纹防护')

                        # 如果自动下载也失败，使用基本防护
                        if not plugin_found:
                            temp_cthulhujs_manager.setup_plugin_directory()
                            self.logger.log('🛡️ 使用基本反指纹防护')
                            self.logger.log('💡 要使用真实插件，请运行: python cthulhujs_plugin.py')

                    # 验证并加载插件
                    is_valid, message = temp_cthulhujs_manager.validate_plugin()
                    if is_valid:
                        plugin_path = temp_cthulhujs_manager.get_plugin_path()
                        options.add_extension(plugin_path)
                        self.logger.log(f'🧩 已加载 CthulhuJS Anti-Fingerprint 插件: {plugin_path}')
                    else:
                        self.logger.log(f'⚠️ CthulhuJS 插件验证失败: {message}')

                except Exception as e:
                    self.logger.log(f'⚠️ CthulhuJS 插件加载失败: {e}')
            else:
                self.logger.log('⚠️ CthulhuJS 插件已禁用 (DRISSON_CTHULHUJS_PLUGIN=false)')

            # 使用更可靠的扩展加载方式
            try:
                from pathlib import Path as _Path
                ext_dirs = []

                # 收集所有存在的扩展目录
                for _name in ['cthulhujs_anti_fingerprint', 'canvas_fingerprint_defender', 'proxy_auth_extension']:
                    _d = _Path(__file__).parent / _name
                    if _d.exists():
                        ext_dirs.append(str(_d))
                        self.logger.log(f'📁 发现扩展目录: {_d}')

                if ext_dirs:
                    # 使用 --load-extension 参数直接加载所有扩展
                    _ext_arg = ','.join(ext_dirs)
                    options.set_argument(f'--load-extension={_ext_arg}')

                    # 不使用 --disable-extensions-except，因为可能会干扰其他功能
                    # options.set_argument(f'--disable-extensions-except={_ext_arg}')

                    self.logger.log(f'🧩 使用 --load-extension 加载扩展: {len(ext_dirs)} 个')
                    for i, ext_dir in enumerate(ext_dirs, 1):
                        self.logger.log(f'   {i}. {ext_dir}')
                else:
                    self.logger.log('⚠️ 未发现任何扩展目录')

            except Exception as _e:
                self.logger.log(f'⚠️ 扩展加载参数设置失败: {_e}')

            # 设置用户代理 - 根据操作系统动态选择，确保英文环境
            system = platform.system().lower()
            if system == 'linux':
                # Ubuntu/Linux User Agent
                user_agent = 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
            elif system == 'darwin':
                # macOS User Agent
                user_agent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
            else:
                # Windows User Agent (默认)
                user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'

            options.set_user_agent(user_agent)
            self.logger.log(f'🤖 设置用户代理: {system.title()} - Chrome 137.0.0.0')

            # 创建页面
            self.page = ChromiumPage(addr_or_opts=options)

            # 设置超时 - 使用正确的 DrissionPage API
            self.page.set.timeouts(base=self.config.page_timeout / 1000)

            # 初始化指纹防护插件管理器
            self.fingerprint_defender_plugin = FingerprintDefenderPlugin(self.logger)

            # 初始化高级指纹防护器
            self.advanced_fingerprint_protection = AdvancedFingerprintProtection(self.page, self.logger)

            # 初始化 Canvas 随机化器
            self.canvas_randomizer = CanvasRandomizer(self.page, self.logger)

            # 立即激活指纹防护（在任何页面导航之前）
            if self.config.fingerprint_protection:
                self._activate_fingerprint_protection()

            self.logger.log('✅ DrissionPage 浏览器启动成功')

            # 记录浏览器启动步骤
            self.logger.capture_step(self.page, 'browser_started', 'DrissionPage 浏览器启动成功')

            return True

        except Exception as error:
            self.logger.error('❌ DrissionPage 浏览器启动失败', error)
            raise error

    def navigate_to_page(self, url):
        """导航到页面"""
        self.logger.log(f'🌐 导航到页面: {url}')

        try:
            self.page.get(url)

            # 根据是否使用代理调整等待时间
            wait_time = 5 if self.config.drissionpage_proxy else 2
            self.logger.log(f'⏳ 等待页面加载 {wait_time} 秒...')
            time.sleep(wait_time)

            # 简化页面检查：只记录状态，不进行刷新或重启
            try:
                result = self.page.run_js("document.readyState", timeout=3)
                self.logger.log(f'📄 页面状态: {result}')
            except Exception as e:
                self.logger.log(f'⚠️ 无法获取页面状态: {str(e)}，但继续执行')



            # 指纹防护已在浏览器启动时激活
            if self.config.fingerprint_protection:
                self.logger.log('🛡️ 指纹防护系统已在浏览器启动时激活')
            else:
                self.logger.log('⚠️ 指纹防护已禁用 (DRISSON_FINGERPRINT_PROTECTION=false)')

            self.logger.capture_step(self.page, 'page_loaded', f'页面加载完成: {url}')
            self.logger.log('✅ 页面导航成功')
            return True

        except Exception as error:
            self.logger.error('❌ 页面导航失败', error)
            self.logger.capture_error(self.page, 'navigation_failed', str(error))
            raise error

    def _check_page_health(self):
        """检查页面健康状态"""
        try:
            # 使用更长的超时时间，特别是在使用代理的情况下
            timeout = 15 if self.config.drissionpage_proxy else 10

            # 尝试执行简单的JavaScript来检查页面是否响应
            result = self.page.run_js("document.readyState", timeout=timeout)

            # 接受更多的页面状态
            if result in ['complete', 'interactive']:
                return True

            # 如果 document.readyState 不是预期值，尝试其他检查
            # 检查页面是否至少有基本的DOM结构
            try:
                has_body = self.page.run_js("!!document.body", timeout=timeout)
                if has_body:
                    self.logger.log(f'⚠️ 页面状态为 {result}，但DOM结构正常，继续执行')
                    return True
            except Exception:
                pass

            self.logger.log(f'⚠️ 页面状态检查: document.readyState = {result}')
            return False

        except Exception as e:
            self.logger.log(f'⚠️ 页面健康检查异常: {str(e)}')
            # 在代理环境下，JavaScript 执行超时不一定意味着页面有问题
            if self.config.drissionpage_proxy:
                self.logger.log('🌐 代理环境下忽略JavaScript执行超时，假设页面正常')
                return True
            return False

    def _restart_browser(self):
        """重启浏览器"""
        try:
            self.logger.log('🔄 重启浏览器...')
            if self.page:
                self.page.quit()
            time.sleep(2)
            self.init_browser()
            self.logger.log('✅ 浏览器重启成功')
        except Exception as e:
            self.logger.error('❌ 浏览器重启失败', e)
            raise e

    def handle_captcha(self):
        """处理验证码"""
        self.logger.log('🔍 检查是否存在验证码...')

        try:
            # 等待页面稳定
            time.sleep(2)
            self.logger.capture_step(self.page, 'before_captcha_check', '验证码检查前的页面状态')

            # 检查多种验证码类型
            captcha_selectors = [
                'div[data-captcha-sitekey]',           # Auth0 v2 / Turnstile
                'iframe[src*="recaptcha"]',            # reCAPTCHA
                'iframe[src*="hcaptcha"]',             # hCaptcha
                '.cf-turnstile',                       # Cloudflare Turnstile
                '[id*="turnstile"]',                   # Turnstile ID
                '[class*="turnstile"]',                # Turnstile class
                '.g-recaptcha',                        # Google reCAPTCHA
                '[id*="recaptcha"]',                   # reCAPTCHA ID
                '[class*="captcha"]',                  # 通用验证码
                '[id*="captcha"]'                      # 验证码 ID
            ]

            captcha_found = False
            captcha_type = ''

            for selector in captcha_selectors:
                captcha_element = self.page.ele(selector, timeout=1)
                if captcha_element:
                    captcha_found = True
                    captcha_type = selector
                    self.logger.log(f'🤖 检测到验证码: {selector}')
                    break

            if captcha_found:
                self.logger.capture_step(self.page, 'captcha_detected', f'检测到验证码: {captcha_type}')

                # 这个初始验证码总是自动解决
                self.logger.log('🚀 开始自动解决初始验证码...')

                # 启用高分数模式
                self.captcha_handler.enable_high_score_mode()

                # 获取验证码信息
                current_url = self.page.url
                site_key_element = self.page.ele('div[data-captcha-sitekey]', timeout=5)
                site_key = None

                if site_key_element:
                    site_key = site_key_element.attr('data-captcha-sitekey')
                    self.logger.log(f'🔑 获取到 siteKey: {site_key}')

                # 如果没有获取到，尝试其他方法 - 参考 real-browser-automation 的 detectCaptcha
                if not site_key:
                    js_result = self.page.run_js("""
                        // 检测 Turnstile - 参考 real-browser-automation
                        const turnstileIframes = document.querySelectorAll('iframe[src*="challenges.cloudflare.com"]');
                        if (turnstileIframes.length > 0) {
                            const src = turnstileIframes[0].src;
                            const match = src.match(/0x4[A-Za-z0-9]{20,}/);
                            if (match) return { found: true, siteKey: match[0], source: 'iframe' };
                        }

                        const siteKeyElements = document.querySelectorAll('[data-sitekey]');
                        if (siteKeyElements.length > 0) {
                            const sitekey = siteKeyElements[0].getAttribute('data-sitekey');
                            if (sitekey && sitekey.startsWith('0x4')) {
                                return { found: true, siteKey: sitekey, source: 'data-sitekey' };
                            }
                        }

                        // 检查 data-captcha-sitekey
                        const captchaSiteKeyElements = document.querySelectorAll('[data-captcha-sitekey]');
                        if (captchaSiteKeyElements.length > 0) {
                            const sitekey = captchaSiteKeyElements[0].getAttribute('data-captcha-sitekey');
                            if (sitekey) {
                                return { found: true, siteKey: sitekey, source: 'data-captcha-sitekey' };
                            }
                        }

                        return { found: false };
                    """)

                    if js_result and js_result.get('found'):
                        site_key = js_result.get('siteKey')
                        source = js_result.get('source')
                        self.logger.log(f'🔑 通过JavaScript获取到 siteKey: {site_key} (来源: {source})')
                    else:
                        self.logger.log('⚠️ 未能获取到 siteKey')

                self.logger.capture_step(self.page, 'before_captcha_solve', f'开始解决验证码: {captcha_type}')

                success = False

                # 根据验证码类型使用相应的处理方法
                if captcha_type == 'div[data-captcha-sitekey]' and site_key:
                    # Turnstile 验证码
                    self.logger.log('🎯 使用 YesCaptcha 处理 Turnstile 验证码...')
                    success = self.captcha_handler.handle_turnstile(self.page, current_url, site_key)
                else:
                    # 其他类型验证码，尝试通用处理
                    self.logger.log('🎯 使用通用验证码处理...')
                    success = False

                if success:
                    self.logger.log('✅ 初始验证码解决成功')
                    self.logger.capture_step(self.page, 'captcha_solved', '初始验证码解决成功')

                    # 等待5秒让页面处理token
                    time.sleep(5)
                    self.logger.capture_step(self.page, 'after_captcha_wait', '验证码解决后等待5秒')

                    # 检查token注入后的状态
                    after_token_status = self.check_captcha_status()
                    self.logger.log(f'Token注入后状态: {json.dumps(after_token_status, indent=2)}')

                    self.logger.capture_step(self.page, 'after_token_injection', 'Token注入后状态检查')

                    return True
                else:
                    self.logger.warn('⚠️ 初始验证码解决失败')
                    self.logger.capture_step(self.page, 'captcha_failed', '初始验证码解决失败')
                    raise Exception('初始验证码解决失败')
            else:
                self.logger.log('✅ 未检测到验证码')
                self.logger.capture_step(self.page, 'no_captcha_found', '未检测到验证码')
                return True

        except Exception as error:
            self.logger.error('❌ 验证码处理失败', error)
            self.logger.capture_error(self.page, 'captcha_error', str(error))
            raise error

    def find_input_field(self, selectors):
        """查找输入框"""
        for selector in selectors:
            try:
                input_element = self.page.ele(selector, timeout=2)
                if input_element:
                    self.logger.log(f'找到输入框: {selector}')
                    return input_element
            except Exception as e:
                self.logger.log(f'选择器 {selector} 未找到元素: {str(e)}')
                continue

        # 如果所有选择器都失败，尝试更宽泛的搜索
        try:
            all_inputs = self.page.eles('input', timeout=5)
            self.logger.log(f'页面中找到 {len(all_inputs)} 个input元素')
            for input_elem in all_inputs:
                input_type = input_elem.attr('type') or ''
                input_name = input_elem.attr('name') or ''
                input_id = input_elem.attr('id') or ''
                self.logger.log(f'Input元素: type={input_type}, name={input_name}, id={input_id}')

                # 检查是否是邮箱相关的输入框
                if (input_name in ['username', 'email'] or
                    input_id in ['username', 'email'] or
                    input_type == 'email'):
                    self.logger.log(f'找到匹配的输入框: name={input_name}, id={input_id}, type={input_type}')
                    return input_elem
        except Exception as e:
            self.logger.log(f'宽泛搜索失败: {str(e)}')

        return None

    def click_button_containing(self, keywords, timeout=15):
        """点击包含特定关键词的按钮 - 模拟真人行为"""
        import random
        import json

        try:
            self.logger.log(f'🔍 寻找包含关键词的按钮: {keywords}')

            # 模拟真人寻找按钮的过程 - 随机等待0.5-1.5秒
            search_delay = random.uniform(0.5, 1.5)
            time.sleep(search_delay)

            # 首先尝试精确的选择器 - 针对验证码页面优化
            primary_selectors = [
                'form[action*="passwordless-email-challenge"] button[data-action-button-primary="true"]',
                'button[data-action-button-primary="true"][type="submit"][name="action"][value="default"]',
                'button[data-action-button-primary="true"]',
                'button[type="submit"]._button-login-id',
                'button[type="submit"][name="action"]',
                'button[type="submit"]',
                'input[type="submit"]'
            ]

            for selector in primary_selectors:
                try:
                    button = self.page.ele(selector, timeout=2)
                    if button:
                        # 获取按钮文本 - 优先使用JavaScript获取
                        text = self.page.run_js(f"""
                            const btn = document.querySelector('{selector}');
                            if (btn) {{
                                return btn.innerText || btn.textContent || btn.value || btn.getAttribute('aria-label') || '';
                            }}
                            return '';
                        """) or ''
                        text = text.strip()

                        self.logger.log(f'找到按钮: {selector}, 文本: "{text}"')

                        for keyword in keywords:
                            if keyword.lower() in text.lower():
                                self.logger.log(f'✅ 匹配关键词 "{keyword}"，准备点击按钮')

                                # 模拟真人点击前的行为
                                return self._simulate_human_button_click(selector, keyword)

                except Exception as e:
                    self.logger.log(f'选择器 {selector} 失败: {str(e)}')
                    continue

            # 如果精确选择器没找到，使用JavaScript全局搜索
            self.logger.log('精确选择器未找到，使用智能搜索...')

            # 模拟真人重新扫描页面的行为
            time.sleep(random.uniform(0.8, 1.5))

            js_result = self.page.run_js(f"""
                const keywords = {json.dumps(keywords)};
                const candidates = Array.from(document.querySelectorAll('button, [role="button"], input[type="submit"], a[href*="continue"]'));

                function isVisible(el) {{
                    const rect = el.getBoundingClientRect();
                    const style = getComputedStyle(el);
                    return rect.width > 0 && rect.height > 0 &&
                           style.visibility !== 'hidden' &&
                           style.display !== 'none' &&
                           !el.disabled;
                }}

                for (const el of candidates) {{
                    const text = (el.innerText || el.textContent || el.value || el.getAttribute('aria-label') || '').trim().toLowerCase();
                    if (!text) continue;

                    for (const keyword of keywords) {{
                        if (text.includes(keyword.toLowerCase())) {{
                            if (isVisible(el)) {{
                                // 返回找到的元素信息
                                return {{
                                    found: true,
                                    tagName: el.tagName,
                                    text: text,
                                    className: el.className,
                                    id: el.id,
                                    selector: el.tagName.toLowerCase() +
                                             (el.id ? '#' + el.id : '') +
                                             (el.className ? '.' + el.className.split(' ')[0] : '')
                                }};
                            }}
                        }}
                    }}
                }}

                return {{ found: false }};
            """)

            if js_result and js_result.get('found'):
                selector = js_result.get('selector', 'button')
                matched_text = js_result.get('text', '')
                self.logger.log(f'✅ JavaScript找到匹配按钮: {selector}, 文本: "{matched_text}"')

                # 模拟真人点击
                return self._simulate_human_button_click(selector, matched_text)

            self.logger.log('❌ 未找到匹配的按钮')
            return False

        except Exception as e:
            self.logger.log(f'❌ 按钮点击失败: {str(e)}')
            return False

    def _simulate_human_button_click(self, selector, keyword):
        """模拟真人点击按钮的行为"""
        import random

        try:
            # 1. 模拟真人定位到按钮的视觉过程
            self.logger.log(f'👁️ 定位到按钮，准备点击...')
            time.sleep(random.uniform(0.3, 0.8))

            # 2. 确保按钮可见和可交互 - 模拟真人会滚动到按钮位置
            # 转义 selector 中的单引号，避免 JS 语法错误
            escaped_selector = selector.replace("'", "\\'")
            self.page.run_js(f"""
                const btn = document.querySelector('{escaped_selector}');
                if (btn) {{
                    // 模拟真人滚动行为 - 滚动到按钮中心位置
                    btn.scrollIntoView({{ behavior: 'smooth', block: 'center' }});

                    // 确保按钮可交互
                    btn.disabled = false;
                    btn.removeAttribute('disabled');
                    btn.style.pointerEvents = 'auto';
                    btn.style.visibility = 'visible';
                    if (getComputedStyle(btn).display === 'none') {{
                        btn.style.display = 'block';
                    }}
                }}
            """)

            # 3. 等待滚动完成 - 模拟真人视觉确认
            time.sleep(random.uniform(0.5, 1.2))

            # 4. 模拟鼠标悬停行为
            self.logger.log(f'🖱️ 鼠标移动到按钮上...')
            try:
                button = self.page.ele(selector, timeout=3)
                if button:
                    # 模拟鼠标悬停
                    button.hover()
                    time.sleep(random.uniform(0.2, 0.5))
            except:
                pass

            # 5. 模拟真人点击前的短暂犹豫
            hesitation_time = random.uniform(0.1, 0.4)
            time.sleep(hesitation_time)

            # 6. 尝试真实点击
            self.logger.log(f'🖱️ 点击按钮...')
            try:
                button = self.page.ele(selector, timeout=3)
                if button:
                    button.click()
                    self.logger.log(f'✅ 按钮点击成功')

                    # 模拟点击后的短暂等待 - 真人会等待页面响应
                    time.sleep(random.uniform(0.8, 1.5))
                    return True
            except Exception as click_error:
                self.logger.log(f'⚠️ 直接点击失败: {str(click_error)}，使用JavaScript点击')

                # 7. JavaScript点击兜底 - 但仍然模拟真人行为
                try:
                    # 模拟真人在点击失败后的短暂停顿
                    time.sleep(random.uniform(0.3, 0.7))

                    self.page.run_js(f"""
                        const btn = document.querySelector('{selector}');
                        if (btn) {{
                            // 模拟真人的点击事件
                            const event = new MouseEvent('click', {{
                                bubbles: true,
                                cancelable: true,
                                view: window
                            }});
                            btn.dispatchEvent(event);
                        }}
                    """)

                    self.logger.log(f'✅ JavaScript点击成功')

                    # 模拟点击后等待
                    time.sleep(random.uniform(0.8, 1.5))
                    return True

                except Exception as js_error:
                    self.logger.log(f'❌ JavaScript点击也失败: {str(js_error)}')
                    return False

        except Exception as e:
            self.logger.log(f'❌ 模拟人类点击失败: {str(e)}')
            return False

    def ensure_turnstile_token_in_form(self):
        """确保 turnstile token 在主表单中"""
        try:
            self.page.run_js("""
                const primaryForm = document.querySelector('form[data-form-primary="true"], form[action*="/login"], form[action*="/identifier"]') || document.querySelector('form');
                if (!primaryForm) return;

                // 查找 token 输入
                const tokenInputs = Array.from(document.querySelectorAll('input[name="cf-turnstile-response"], input[name*="turnstile"], input[name*="captcha"], input[name*="token"]'));
                tokenInputs.forEach(input => {
                    if (input.form !== primaryForm) {
                        // 迁移 token 到主表单
                        const clone = input.cloneNode(true);
                        primaryForm.appendChild(clone);
                    }
                });
            """)
        except Exception as e:
            self.logger.warn(f'ensureTurnstileTokenInForm 出错: {str(e)}')

    def programmatic_submit_primary_form(self):
        """程序化提交主表单"""
        try:
            self.page.run_js("""
                // Prefer requestSubmit when available; target verification form first
                const primaryForm = document.querySelector(
                    'form[action*="/passwordless-email-challenge"], form#action-form, form[data-form-primary="true"], form[action*="/login"], form[action*="/identifier"]'
                ) || document.querySelector('form');
                if (primaryForm) {
                    // Ensure code input is present and events fired
                    const code = document.querySelector('input[name="code"], #code');
                    if (code) {
                        code.dispatchEvent(new Event('input', { bubbles: true }));
                        code.dispatchEvent(new Event('change', { bubbles: true }));
                        code.blur();
                    }
                    if (typeof primaryForm.requestSubmit === 'function') {
                        primaryForm.requestSubmit();
                    } else {
                        primaryForm.submit();
                    }
                }
            """)
        except Exception as e:
            self.logger.warn(f'programmaticSubmitPrimaryForm 出错: {str(e)}')

    def sync_captcha_hidden_input(self):
        """同步 YesCaptcha 注入的 token 到 Auth0 ULP 期望的隐藏字段"""
        try:
            status = self.page.run_js("""
                const tokenInput = document.querySelector('input[name="cf-turnstile-response"]');
                const captchaInput = document.querySelector('div[data-captcha-provider="auth0_v2"] input[name="captcha"], input[name="captcha"]');
                let copied = false;

                if (tokenInput && tokenInput.value) {
                    if (captchaInput) {
                        // 将 token 复制到 Auth0 的隐藏字段
                        captchaInput.value = tokenInput.value;
                        // 触发事件以通过前端验证器
                        captchaInput.dispatchEvent(new Event('input', { bubbles: true }));
                        captchaInput.dispatchEvent(new Event('change', { bubbles: true }));
                        // 去除错误/待处理样式（如果有）
                        const container = captchaInput.closest('.ulp-captcha-container') || document.querySelector('.ulp-captcha-container');
                        if (container) {
                            try { container.classList.remove('c7cf794f8'); } catch (e) {}
                        }
                        copied = true;
                    }
                }
                return {
                    copied,
                    tokenLen: tokenInput?.value?.length || 0,
                    hasCaptchaInput: !!captchaInput,
                    captchaLen: captchaInput?.value?.length || 0
                };
            """)

            self.logger.log(f'🔄 同步captcha隐藏字段: {json.dumps(status)}')
            self.logger.capture_step(self.page, 'captcha_hidden_synced', '同步captcha隐藏字段到Auth0', logical_step=7)
            return status.get('copied', False)
        except Exception as e:
            self.logger.warn(f'syncCaptchaHiddenInput 出错: {str(e)}')
            return False

    def enter_email(self, email):
        """输入邮箱"""
        self.logger.log(f'📧 输入邮箱: {email}')

        try:
            # 等待页面完全加载
            self.logger.log('等待页面完全加载...')
            time.sleep(3)

            # 简化页面检查：只记录状态，不重启浏览器
            try:
                result = self.page.run_js("document.readyState", timeout=3)
                self.logger.log(f'📄 页面状态: {result}')
            except Exception as e:
                self.logger.log(f'⚠️ 无法获取页面状态: {str(e)}，但继续执行')

            self.logger.capture_step(self.page, 'before_email_input', '准备输入邮箱前的页面状态')

            # 使用与 headless-automation 完全相同的选择器列表
            email_input_selectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[name="username"]',
                'input[id="username"]',
                'input[inputmode="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="Email"]',
                'input[id*="email"]',
                'input[class*="email"]'
            ]

            # 直接尝试最常见的选择器
            email_input = None

            # 首先尝试最可能的选择器
            primary_selectors = [
                'input[name="username"]',
                'input[id="username"]',
                '#username',
                'input[inputmode="email"]'
            ]

            for selector in primary_selectors:
                try:
                    self.logger.log(f'尝试选择器: {selector}')
                    email_input = self.page.ele(selector, timeout=3)
                    if email_input:
                        self.logger.log(f'✅ 找到邮箱输入框: {selector}')
                        break
                except Exception as e:
                    self.logger.log(f'选择器 {selector} 失败: {str(e)}')
                    continue

            # 如果还没找到，尝试所有选择器
            if not email_input:
                self.logger.log('尝试所有邮箱输入框选择器...')
                for selector in email_input_selectors:
                    try:
                        email_input = self.page.ele(selector, timeout=2)
                        if email_input:
                            self.logger.log(f'✅ 找到邮箱输入框: {selector}')
                            break
                    except:
                        continue

            # 最后尝试：使用JavaScript验证并直接获取
            if not email_input:
                self.logger.log('使用JavaScript直接查找输入框...')
                self.logger.capture_step(self.page, 'before_js_search', 'JavaScript查找前的页面状态')

                js_result = self.page.run_js("""
                    const element = document.querySelector('input[name="username"]') ||
                                   document.querySelector('input[id="username"]') ||
                                   document.querySelector('#username');

                    if (element) {
                        return {
                            found: true,
                            tagName: element.tagName,
                            type: element.type,
                            name: element.name,
                            id: element.id,
                            className: element.className,
                            value: element.value
                        };
                    }

                    return { found: false };
                """)

                self.logger.log(f'JavaScript查找结果: {js_result}')

                if js_result and js_result.get('found'):
                    # 如果JavaScript找到了，再次尝试获取
                    try:
                        email_input = self.page.ele('#username', timeout=5)
                        self.logger.log('✅ 通过JavaScript验证后成功获取元素')
                    except:
                        try:
                            email_input = self.page.ele('input[name="username"]', timeout=5)
                            self.logger.log('✅ 通过name属性成功获取元素')
                        except:
                            pass

                if not email_input:
                    self.logger.capture_step(self.page, 'email_input_not_found', '未找到邮箱输入框')
                    raise Exception('未找到邮箱输入框')

            self.logger.log(f'找到邮箱输入框: {email_input}')
            self.logger.capture_step(self.page, 'email_input_found', '找到邮箱输入框')

            # 滚动到元素并聚焦
            email_input.scroll.to_see()
            self.logger.capture_step(self.page, 'after_scroll', '滚动到邮箱输入框')

            email_input.click()
            time.sleep(0.4)
            self.logger.capture_step(self.page, 'after_click', '点击邮箱输入框后')

            # 清空现有内容
            email_input.clear()
            time.sleep(0.2)
            self.logger.capture_step(self.page, 'after_clear', '清空输入框后')

            # 输入邮箱
            email_input.input(email)
            time.sleep(0.3)
            self.logger.capture_step(self.page, 'after_input', f'输入邮箱后: {email}')

            # 触发事件
            self.page.run_js(f"""
                const input = document.querySelector('input[name="username"], input[id="username"], input[type="email"]');
                if (input) {{
                    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    input.blur();
                }}
            """)
            time.sleep(0.2)
            self.logger.capture_step(self.page, 'after_events', '触发事件后')

            # 验证是否完整写入
            written_value = email_input.value
            if not written_value or written_value.strip() != email:
                self.logger.log(f'邮箱写入不完整，使用DOM直接设置: written="{written_value}"')
                # 回退到 DOM 直接设置
                self.page.run_js(f"""
                    const input = document.querySelector('input[name="username"], input[id="username"], input[type="email"]');
                    if (input) {{
                        input.value = '{email}';
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        input.blur();
                    }}
                """)
                time.sleep(0.2)
                self.logger.capture_step(self.page, 'after_dom_set', 'DOM直接设置后')

            # 二次校验确保一致
            final_value = email_input.value
            if final_value.strip() != email:
                self.logger.capture_step(self.page, 'email_verification_failed', f'邮箱验证失败: got="{final_value}" expected="{email}"')
                raise Exception(f'邮箱写入不完整: got="{final_value}" expected="{email}"')

            self.logger.log(f'邮箱已填入: {email}')
            self.logger.capture_step(self.page, 'email_entered', f'邮箱输入完成: {email}')

            self.logger.log('✅ 邮箱输入成功')
            return True

        except Exception as error:
            self.logger.error('❌ 邮箱输入失败', error)
            self.logger.capture_error(self.page, 'email_input_failed', str(error))
            raise error

    def click_continue(self):
        """点击继续按钮"""
        self.logger.log('🔄 点击继续按钮...')

        try:
            before_url = self.page.url

            # 📝 邮箱页面 Continue 点击

            # 优先确保 token 在主表单里，并同步到 Auth0 的 captcha 隐藏字段
            self.ensure_turnstile_token_in_form()
            self.sync_captcha_hidden_input()

            # 在点击前确保按钮可用
            self.page.run_js("""
                const btn = document.querySelector('button[type="submit"]._button-login-id, button[type="submit"][data-action-button-primary="true"], button._button-login-id');
                const form = document.querySelector('form[data-form-primary="true"]') || document.querySelector('form');
                if (btn) {
                    btn.removeAttribute('disabled');
                    btn.removeAttribute('aria-disabled');
                }
                if (form) {
                    // 触发表单验证器
                    const email = form.querySelector('input[name="username"], input[type="email"]');
                    if (email) {
                        email.dispatchEvent(new Event('input', { bubbles: true }));
                        email.dispatchEvent(new Event('change', { bubbles: true }));
                        email.blur();
                    }
                    const captcha = form.querySelector('input[name="captcha"], input[name="cf-turnstile-response"]');
                    if (captcha && captcha.value) {
                        captcha.dispatchEvent(new Event('input', { bubbles: true }));
                        captcha.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                }
            """)

            # 使用与 headless-automation 相同的按钮点击逻辑
            # 捕获点击前的状态（逻辑步骤7）
            try:
                self.logger.capture_step(self.page, 'before_continue_click', '点击Continue前的页面状态', logical_step=7)
            except Exception:
                pass

            continue_clicked = self.click_button_containing(['Continue', 'Next', '继续', 'Submit'])
            if continue_clicked:
                self.logger.log('✅ Continue按钮点击成功')

                # 📝 邮箱页面 Continue 点击成功 - 等待跳转到验证码页面

            else:
                self.logger.log('❌ 未找到Continue按钮')
                self.logger.capture_step(self.page, 'continue_button_not_found', '未找到Continue按钮', logical_step=7)
                raise Exception('未找到Continue按钮')

            # 等待页面跳转或验证码输入框出现
            navigated = False
            try:
                # 等待URL变化或验证码输入框出现
                for _ in range(15):  # 15秒超时
                    current_url = self.page.url
                    code_input = self.page.ele('input[name="code"], input[name="verification_code"], input[name="verificationCode"], input[autocomplete="one-time-code"]', timeout=1)

                    if current_url != before_url or code_input:
                        navigated = True
                        break
                    time.sleep(1)
            except:
                pass

            # 若仍未跳转，直接提交主表单作为兜底
            after_try_url = self.page.url
            if not navigated and after_try_url == before_url:
                self.logger.warn('⚠️ URL未变化，先再次同步captcha字段后使用表单.submit() 兜底提交')
                self.sync_captcha_hidden_input()
                self.programmatic_submit_primary_form()

                # 再等待一次
                try:
                    for _ in range(8):  # 8秒超时
                        if self.page.url != before_url:
                            navigated = True
                            break
                        time.sleep(1)
                except:
                    pass

            self.logger.capture_step(self.page, 'continue_clicked', '继续按钮点击完成', logical_step=7)

            # 处理邮箱页面可能出现的验证码
            self.logger.log('🔍 检查邮箱页面是否有验证码...')
            # 捕获进入“检查验证码”步骤前的状态（逻辑步骤7，延续）
            try:
                self.logger.capture_step(self.page, 'before_handle_captcha_if_present', 'Continue后，检查验证码前', logical_step=7)
            except Exception:
                pass

            self.handle_captcha_if_present()

            # 严格断言：如果仍未跳转且也未出现验证码输入框，则认为点击失败
            still_on_same_url = self.page.url == before_url
            has_code_input = bool(self.page.ele('input[name="code"], input[name="verification_code"], input[name="verificationCode"], input[autocomplete="one-time-code"]', timeout=1))

            if still_on_same_url and not has_code_input:
                self.logger.capture_error(self.page, 'continue_not_navigated', '点击Continue后未跳转且未出现验证码输入框')
                raise Exception('Continue 点击后未跳转，仍停留在邮箱页面')

            return True

        except Exception as error:
            self.logger.error('❌ 继续按钮点击失败', error)
            self.logger.capture_error(self.page, 'continue_click_failed', str(error))
            raise error

    def handle_captcha_if_present(self):
        """检查当前页面是否存在验证码并处理（自动提交页采用'最后一刻替换 token'策略）"""
        self.logger.log('🔍 检查当前页面是否存在验证码...')

        try:
            time.sleep(2)  # 等待页面稳定

            captcha_info = self.detect_captcha()
            if not captcha_info.get('has_turnstile') and not captcha_info.get('has_recaptcha'):
                self.logger.log('✅ 当前页面未检测到验证码')
                return True

            self.logger.log('🤖 当前页面检测到验证码')
            self.logger.capture_step(self.page, 'page_captcha_detected', '页面验证码检测')

            success = False

            if captcha_info.get('has_turnstile') and captcha_info.get('site_key'):
                self.logger.log('🎯 使用 YesCaptcha 处理页面 Turnstile 验证码...')
                success = self.captcha_handler.handle_turnstile(self.page, captcha_info['current_url'], captcha_info['site_key'])
            elif captcha_info.get('has_recaptcha'):
                # 检测是否为自动提交页面（Verify human）
                is_auto_submit_page = self.page.run_js("""
                    return !!(document.querySelector('#action-form') ||
                              document.body.innerHTML.includes('Auto-submit logic') ||
                              document.body.innerHTML.includes('onClick'));
                """)

                if is_auto_submit_page and self.config.drissionpage_recaptcha_solve:
                    self.logger.log('🎯 检测到自动提交页面，使用 YesCaptcha 获取 reCAPTCHA v3 Enterprise token...')

                    # 如果检测到了 site key，更新配置
                    if captcha_info.get('site_key'):
                        self.logger.log(f'🔑 检测到 reCAPTCHA site key: {captcha_info["site_key"]}')
                        self.captcha_handler.recaptcha_site_key = captcha_info['site_key']

                    success = self.captcha_handler.handle_recaptcha_enterprise(self.page)

                    # 在自动提交场景下，注入'最后一刻替换 token'补丁（仅当前表单，非全局 hook）
                    try:
                        self.page.run_js("""
                            (function() {
                                const form = document.getElementById('action-form')
                                           || document.querySelector('form[action*="/terms-accept"]')
                                           || document.querySelector('form[action*="/login"]')
                                           || document.querySelector('form');
                                if (!form || form.__augPatched) return;
                                const native = form.submit.bind(form);
                                form.submit = function() {
                                    try {
                                        const input = document.getElementById('g-recaptcha-response')
                                                   || form.querySelector('input[name="g-recaptcha-response"]');
                                        if (input && window.__AUG_TOKEN) input.value = window.__AUG_TOKEN;
                                        const errs = document.getElementById('client-errors');
                                        if (errs) errs.value = '[]';
                                    } catch(e) {}
                                    return native();
                                };
                                form.__augPatched = true;
                            })();
                        """)
                    except Exception as e:
                        self.logger.warn(f"注入提交前补丁失败: {str(e)}")
                else:
                    self.logger.log('⏸️ reCAPTCHA 自动解决已禁用或非自动提交页面')
                    success = True  # 跳过处理

            if success:
                self.logger.log('✅ 页面验证码处理成功')
                self.logger.capture_step(self.page, 'page_captcha_solved', '页面验证码解决成功')
                time.sleep(5)  # 给页面时间处理 token
                return True
            else:
                self.logger.warn('⚠️ 页面验证码处理失败')
                self.logger.capture_step(self.page, 'page_captcha_failed', '页面验证码处理失败')
                return False

        except Exception as error:
            self.logger.error('❌ 页面验证码处理出错', error)
            self.logger.capture_error(self.page, 'page_captcha_error', str(error))
            return False

    def detect_captcha(self):
        """检测验证码"""
        return self.page.run_js("""
            const result = {
                has_turnstile: false,
                has_recaptcha: false,
                site_key: null,
                current_url: window.location.href
            };

            // 检测 Turnstile
            const turnstileIframes = document.querySelectorAll('iframe[src*="challenges.cloudflare.com"]');
            if (turnstileIframes.length > 0) {
                result.has_turnstile = true;
                const src = turnstileIframes[0].src;
                const match = src.match(/0x4[A-Za-z0-9]{20,}/);
                if (match) result.site_key = match[0];
            }

            const siteKeyElements = document.querySelectorAll('[data-sitekey]');
            if (siteKeyElements.length > 0) {
                const sitekey = siteKeyElements[0].getAttribute('data-sitekey');
                if (sitekey && sitekey.startsWith('0x4')) {
                    result.has_turnstile = true;
                    result.site_key = sitekey;
                }
            }

            // 检测 reCAPTCHA（更全面的检测）
            const recaptchaElements = document.querySelectorAll('iframe[src*="recaptcha"], .g-recaptcha, [data-sitekey*="6L"]');
            if (recaptchaElements.length > 0) {
                result.has_recaptcha = true;
            }

            // 检测 reCAPTCHA Enterprise 脚本
            const recaptchaScripts = document.querySelectorAll('script[src*="recaptcha/enterprise.js"]');
            if (recaptchaScripts.length > 0) {
                result.has_recaptcha = true;
                // 从脚本 URL 提取 site key
                const src = recaptchaScripts[0].src;
                const match = src.match(/render=([^&]+)/);
                if (match) result.site_key = match[1];
            }

            // 检测 reCAPTCHA Enterprise API
            if (window.grecaptcha && window.grecaptcha.enterprise) {
                result.has_recaptcha = true;
            }

            // 检测隐藏的 g-recaptcha-response 字段（自动提交页面特征）
            const hiddenRecaptcha = document.querySelector('input[name="g-recaptcha-response"]');
            if (hiddenRecaptcha) {
                result.has_recaptcha = true;
            }

            // 检测 onClick 函数（自动提交页面特征）
            if (typeof window.onClick === 'function') {
                result.has_recaptcha = true;
            }

            return result;
        """)

    def check_captcha_status(self):
        """检查验证码状态"""
        return self.page.run_js("""
            const result = {
                is_resolved: false,
                turnstile_status: 'unknown',
                hidden_inputs: [],
                visible_elements: [],
                errors: []
            };

            try {
                // 检查Turnstile响应字段
                const turnstileInputs = document.querySelectorAll('input[name="cf-turnstile-response"]');
                turnstileInputs.forEach((input, index) => {
                    result.hidden_inputs.push({
                        type: 'turnstile-response',
                        index,
                        has_value: input.value && input.value.length > 0,
                        value_length: input.value ? input.value.length : 0
                    });

                    if (input.value && input.value.length > 0) {
                        result.is_resolved = true;
                        result.turnstile_status = 'resolved';
                    }
                });

                // 检查其他验证码相关的隐藏字段
                const otherInputs = document.querySelectorAll('input[name*="captcha"], input[name*="token"]');
                otherInputs.forEach((input, index) => {
                    if (input.name !== 'cf-turnstile-response') {
                        result.hidden_inputs.push({
                            type: 'other-captcha',
                            name: input.name,
                            index,
                            has_value: input.value && input.value.length > 0,
                            value_length: input.value ? input.value.length : 0
                        });
                    }
                });

                // 检查可见的验证码元素
                const visibleCaptchaElements = document.querySelectorAll('.cf-turnstile, .g-recaptcha, [data-sitekey]');
                visibleCaptchaElements.forEach((element, index) => {
                    result.visible_elements.push({
                        type: element.className.includes('cf-turnstile') ? 'turnstile' :
                              element.className.includes('g-recaptcha') ? 'recaptcha' : 'unknown',
                        index,
                        is_visible: element.offsetParent !== null,
                        has_data_sitekey: element.hasAttribute('data-sitekey')
                    });
                });

            } catch (error) {
                result.errors.push(error.message);
            }

            return result;
        """)

    def wait_for_verification_page(self):
        """等待验证页面加载并检测验证码输入框"""
        self.logger.log('⏳ 等待验证页面加载...')

        try:
            # 直接检查验证码输入框是否存在
            code_input = None

            # 首先尝试最精确的选择器
            primary_selectors = [
                'input[name="code"]',
                'input[id="code"]',
                '#code'
            ]

            for selector in primary_selectors:
                try:
                    self.logger.log(f'尝试验证码输入框选择器: {selector}')
                    code_input = self.page.ele(selector, timeout=3)
                    if code_input:
                        self.logger.log(f'✅ 找到验证码输入框: {selector}')
                        break
                except Exception as e:
                    self.logger.log(f'选择器 {selector} 失败: {str(e)}')
                    continue

            if not code_input:
                # 使用JavaScript验证
                js_result = self.page.run_js("""
                    const codeInput = document.querySelector('input[name="code"]') ||
                                     document.querySelector('input[id="code"]') ||
                                     document.querySelector('#code');

                    if (codeInput) {
                        return {
                            found: true,
                            tagName: codeInput.tagName,
                            type: codeInput.type,
                            name: codeInput.name,
                            id: codeInput.id,
                            className: codeInput.className
                        };
                    }

                    return { found: false };
                """)

                self.logger.log(f'JavaScript验证码输入框检查结果: {js_result}')

                if js_result and js_result.get('found'):
                    try:
                        code_input = self.page.ele('#code', timeout=5)
                        self.logger.log('✅ 通过JavaScript验证后成功获取验证码输入框')
                    except:
                        pass

            if not code_input:
                raise Exception('未找到验证码输入框')

            self.logger.capture_step(self.page, 'verification_page_loaded', '验证页面加载完成', logical_step=8)

            self.logger.log('✅ 验证页面加载成功')
            return True

        except Exception as error:
            self.logger.error('❌ 验证页面加载失败', error)
            self.logger.capture_error(self.page, 'verification_page_failed', str(error))
            raise error

    def enter_verification_code(self, code):
        """输入验证码 - 模拟真人输入"""
        self.logger.log(f'🔢 输入验证码: {code}')

        try:
            # 直接使用精确的选择器
            code_input = None

            # 首先尝试最精确的选择器
            primary_selectors = [
                'input[name="code"]',
                'input[id="code"]',
                '#code'
            ]

            for selector in primary_selectors:
                try:
                    self.logger.log(f'尝试验证码输入框选择器: {selector}')
                    code_input = self.page.ele(selector, timeout=3)
                    if code_input:
                        self.logger.log(f'✅ 找到验证码输入框: {selector}')
                        break
                except Exception as e:
                    self.logger.log(f'选择器 {selector} 失败: {str(e)}')
                    continue

            if not code_input:
                raise Exception('未找到验证码输入框')

            self.logger.log(f'找到验证码输入框: {code_input}')
            self.logger.capture_step(self.page, 'code_input_found', '找到验证码输入框', logical_step=9)

            # 滚动到元素并聚焦 - 模拟真人操作
            code_input.scroll.to_see()
            self.logger.capture_step(self.page, 'after_scroll_to_code', '滚动到验证码输入框', logical_step=9)

            code_input.click()
            time.sleep(0.5)
            self.logger.capture_step(self.page, 'after_click_code', '点击验证码输入框后', logical_step=9)

            # 清空现有内容
            code_input.clear()
            time.sleep(0.3)
            self.logger.capture_step(self.page, 'after_clear_code', '清空验证码输入框后', logical_step=9)

            # 模拟真人逐字符输入验证码
            self.logger.log('🤖 开始模拟真人输入验证码...')
            for i, char in enumerate(code):
                code_input.input(char)
                time.sleep(0.1 + (i * 0.05))  # 逐渐增加间隔，模拟真人
                self.logger.log(f'输入字符 {i+1}/{len(code)}: {char}')

            time.sleep(0.5)
            self.logger.capture_step(self.page, 'after_input_code', f'输入验证码后: {code}', logical_step=10)

            # 触发事件确保验证码被识别
            self.page.run_js(f"""
                const input = document.querySelector('input[name="code"], input[id="code"], #code');
                if (input) {{
                    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    input.blur();
                }}
            """)
            time.sleep(0.3)
            self.logger.capture_step(self.page, 'after_events_code', '触发验证码事件后', logical_step=10)

            # 验证是否完整写入
            final_value = code_input.value
            if final_value.strip() != code:
                self.logger.log(f'验证码写入不完整，使用DOM直接设置: written="{final_value}"')
                # 回退到 DOM 直接设置
                self.page.run_js(f"""
                    const input = document.querySelector('input[name="code"], input[id="code"], #code');
                    if (input) {{
                        input.value = '{code}';
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        input.blur();
                    }}
                """)
                time.sleep(0.3)
                self.logger.capture_step(self.page, 'after_dom_set_code', 'DOM直接设置验证码后', logical_step=10)

            # 最终校验
            final_value = code_input.value
            if final_value.strip() != code:
                self.logger.capture_step(self.page, 'code_verification_failed', f'验证码验证失败: got="{final_value}" expected="{code}"', logical_step=10)
                raise Exception(f'验证码写入不完整: got="{final_value}" expected="{code}"')

            self.logger.log(f'验证码已填入: {code}')
            self.logger.capture_step(self.page, 'code_entered', f'验证码输入完成: {code}', logical_step=10)

            self.logger.log('✅ 验证码输入成功')
            return True

        except Exception as error:
            self.logger.error('❌ 验证码输入失败', error)
            self.logger.capture_error(self.page, 'code_input_failed', str(error))
            raise error

    def click_verification_continue(self):
        """点击验证码页面的Continue按钮 - 专门针对验证码页面优化"""
        self.logger.log('🔄 点击验证码页面的Continue按钮...')

        try:
            # 模拟真人在输入验证码后的短暂停顿
            import random
            pause_time = random.uniform(1.0, 2.5)
            self.logger.log(f'🤔 输入完成后思考 {pause_time:.1f} 秒...')
            time.sleep(pause_time)

            # 📝 验证码页面 Continue 点击前 - 不在这里注入对抗措施
            # 因为点击后会页面跳转，注入的脚本会丢失
            # 真正的对抗措施将在授权页面加载后注入

            # 捕获点击前状态
            self.logger.capture_step(self.page, 'before_verification_continue', '验证码页面点击Continue前', logical_step=11)

            # 优先在验证码表单内查找主按钮；若无则回退关键词点击；最终兜底 requestSubmit()
            form_info = self.page.run_js("""
                const form = (document.querySelector('input#code, input[name="code"]').form)
                           || document.querySelector('form[action*="passwordless-email-challenge"]')
                           || document.querySelector('#action-form');
                if (!form) return { found: false };
                const btn = form.querySelector('button[data-action-button-primary="true"][type="submit"][name="action"][value="default"]')
                         || form.querySelector('button[data-action-button-primary="true"]')
                         || form.querySelector('button[type="submit"]');
                let uid = null;
                if (btn) { uid = 'aug-' + Math.random().toString(36).slice(2); btn.setAttribute('data-aug-uid', uid); }
                return { found: true, hasBtn: !!btn, uid };
            """)

            # 1) 表单内已找到主按钮 -> 真人模拟点击
            if form_info and form_info.get('found') and form_info.get('hasBtn'):
                selector = f"[data-aug-uid='{form_info.get('uid')}']"
                self.logger.log(f"🔎 在验证码表单内找到主按钮: {selector}")
                clicked = self._simulate_human_button_click(selector, 'continue')
                if clicked:
                    self.logger.log('✅ 验证码页面Continue按钮点击成功')
                    self.logger.capture_step(self.page, 'verification_continue_clicked', '验证码页面Continue点击完成', logical_step=11)
                    response_wait = random.uniform(2.0, 4.0)
                    self.logger.log(f'⏳ 等待页面响应 {response_wait:.1f} 秒...')
                    time.sleep(response_wait)
                    return True
                else:
                    self.logger.log('⚠️ 表单内按钮真实点击失败，先检查验证码再程序化提交表单')
                    # 在提交前检查并处理验证码
                    self.handle_captcha_if_present()
                    self.programmatic_submit_primary_form()
                    self.logger.capture_step(self.page, 'verification_continue_programmatic', '使用表单提交兜底', logical_step=11)
                    time.sleep(random.uniform(1.0, 2.0))
                    return True

            # 2) 未拿到表单按钮，回退关键词点击
            continue_clicked = self.click_button_containing(['Continue', 'Verify', 'Submit', '继续', '验证', '提交'])
            if continue_clicked:
                self.logger.log('✅ 验证码页面Continue按钮点击成功(关键词回退)')
                self.logger.capture_step(self.page, 'verification_continue_clicked', '验证码页面Continue点击完成(关键词回退)', logical_step=11)
                response_wait = random.uniform(2.0, 4.0)
                self.logger.log(f'⏳ 等待页面响应 {response_wait:.1f} 秒...')
                time.sleep(response_wait)
                return True

            # 3) 最终兜底：先检查验证码再直接提交表单
            self.logger.log('⚠️ 未找到Continue按钮，最终兜底：先检查验证码再调用表单提交')
            # 在提交前检查并处理验证码
            self.handle_captcha_if_present()
            self.programmatic_submit_primary_form()
            self.logger.capture_step(self.page, 'verification_continue_programmatic', '使用表单提交兜底', logical_step=11)
            time.sleep(random.uniform(1.0, 2.0))
            return True

        except Exception as error:
            self.logger.error('❌ 验证码页面Continue按钮点击失败', error)
            self.logger.capture_error(self.page, 'verification_continue_failed', str(error))
            raise error

            # 使用与 headless-automation 相同的输入方式
            code_input.click()
            time.sleep(1)  # 等待1秒
            code_input.input(code, clear=True)

            self.logger.capture_step(self.page, 'code_entered', f'验证码输入完成: {code}', logical_step=10)

            self.logger.log('✅ 验证码输入成功')
            return True

        except Exception as error:
            self.logger.error('❌ 验证码输入失败', error)
            self.logger.capture_error(self.page, 'code_input_failed', str(error))
            raise error

    def wait_for_authorization_code(self):
        """等待授权码页面"""
        self.logger.log('⏳ 等待授权码页面...')

        try:
            # 捕获等待开始时的页面状态
            self.logger.capture_step(self.page, 'before_wait_authorization', '开始等待授权码页面', logical_step=12)

            # 授权页面加载后的处理
            self.logger.log('📄 授权页面已加载，开始等待授权码...')

            # 等待授权码出现或复制按钮出现
            for attempt in range(30):  # 30秒超时
                # 检查复制按钮
                copy_button = None
                buttons = self.page.eles('button', timeout=1)
                for btn in buttons:
                    text = btn.text.lower()
                    if 'copy' in text or '复制' in text:
                        copy_button = btn
                        break

                # 检查授权码元素
                code_element = self.page.ele('[data-testid="authorization-code"], .authorization-code, #authorization-code', timeout=1)

                # 检查页面文本中是否包含授权码
                page_text = self.page.html
                has_auth_code = ('authorization_code=' in page_text or
                               'code=' in page_text or
                               'Authorization Code' in page_text or
                               '授权码' in page_text)

                if copy_button or code_element or has_auth_code:
                    self.logger.log(f'✅ 授权码页面检测成功 (第{attempt + 1}次尝试)')
                    break

                # 每5秒capture一次等待状态
                if attempt % 5 == 0 and attempt > 0:
                    self.logger.capture_step(self.page, f'waiting_authorization_{attempt}s', f'等待授权码页面 {attempt}秒', logical_step=12)
                    self.logger.log(f'⏳ 继续等待授权码页面... ({attempt}/30秒)')

                time.sleep(1)
            else:
                self.logger.capture_step(self.page, 'authorization_timeout', '授权码页面等待超时', logical_step=12)
                raise Exception('授权码页面加载超时')

            self.logger.capture_step(self.page, 'authorization_page_loaded', '授权码页面加载完成', logical_step=12)

            self.logger.log('✅ 授权码页面加载成功')
            return True

        except Exception as error:
            self.logger.error('❌ 授权码页面加载失败', error)
            self.logger.capture_error(self.page, 'authorization_page_failed', str(error))
            raise error

    def extract_authorization_code(self):
        """提取授权码"""
        self.logger.log('📋 提取授权码...')

        try:
            # 等待按钮出现，保证页面资源完全加载
            try:
                copy_button = None
                buttons = self.page.eles('button', timeout=10)
                for btn in buttons:
                    text = btn.text.lower()
                    if 'copy' in text or '复制' in text:
                        copy_button = btn
                        break
            except:
                pass

            # 策略0: 劫持 clipboard.writeText 并点击按钮，直接获取 JSON 字符串
            try:
                hijacked = self.page.run_js("""
                    try {
                        window.__authCopied = null;
                        const btn = document.getElementById('copyButton') || Array.from(document.querySelectorAll('button')).find(b => (b.textContent||'').toLowerCase().includes('copy'));
                        if (!btn) return { installed: false };

                        // 尝试覆盖 clipboard.writeText
                        let installed = false;
                        try {
                            const original = navigator.clipboard.writeText;
                            Object.defineProperty(navigator.clipboard, 'writeText', { configurable: true, writable: true, value: (text) => { window.__authCopied = text; return Promise.resolve(); } });
                            installed = true;
                        } catch (e) {
                            // 一些环境不可覆盖，退而求其次：在捕获阶段拦截点击并自行组装
                            btn.addEventListener('click', function () {
                                try {
                                    const scripts = Array.from(document.querySelectorAll('script'));
                                    for (const s of scripts) {
                                        const c = s.textContent || s.innerHTML || '';
                                        const m = c.match(/let\\s+data\\s*=\\s*(\\{[\\s\\S]*?\\})/);
                                        if (m) {
                                            try {
                                                // 直接 eval 对象字面量（仅作用于匹配到的对象文本）
                                                const obj = (0, eval)('(' + m[1] + ')');
                                                if (obj && obj.code && obj.state && obj.tenant_url) {
                                                    window.__authCopied = JSON.stringify(obj);
                                                    break;
                                                }
                                            } catch {}
                                        }
                                    }
                                } catch {}
                            }, { capture: true, once: true });
                        }

                        btn.click();
                        return { installed };
                    } catch (e) {
                        return { installed: false, error: e.message };
                    }
                """)

                # 给页面时间执行监听
                time.sleep(0.8)

                captured = self.page.run_js("return window.__authCopied")
                if captured and captured.strip():
                    self.logger.capture_step(self.page, 'authorization_copied_intercepted', '通过拦截clipboard获取授权码', logical_step=13)
                    self.logger.log(f'📋 通过拦截clipboard获取授权码: {captured[:120]}...')
                    return captured.strip()
            except:
                pass

            # 策略1: 尝试点击复制按钮 + 本地读取剪贴板
            if copy_button:
                copy_button.click()
                self.logger.log('✅ 复制按钮点击成功')
                time.sleep(0.8)
                try:
                    import pyperclip
                    clipboard_content = pyperclip.paste()
                    if clipboard_content and clipboard_content.strip():
                        self.logger.capture_step(self.page, 'authorization_copied', '授权码复制完成', logical_step=13)
                        self.logger.log(f'📋 从剪贴板获取授权码: {clipboard_content[:120]}...')
                        return clipboard_content.strip()
                except Exception as clipboard_error:
                    self.logger.warn('剪贴板读取失败，尝试其他方法')

            # 策略2: 从页面JavaScript中提取完整的授权码数据
            auth_data_from_script = self.page.run_js("""
                try {
                    const scripts = Array.from(document.querySelectorAll('script'));
                    for (let i = 0; i < scripts.length; i++) {
                        const content = scripts[i].textContent || scripts[i].innerHTML || '';
                        // 宽松匹配对象字面量
                        const dataMatch = content.match(/let\\s+data\\s*=\\s*(\\{[\\s\\S]*?\\})/);
                        if (dataMatch) {
                            const objTxt = dataMatch[1];
                            try {
                                const obj = (0, eval)('(' + objTxt + ')');
                                if (obj && obj.code && obj.state && obj.tenant_url) {
                                    return JSON.stringify(obj);
                                }
                            } catch {}
                            // 回退：单字段匹配
                            const codeMatch = objTxt.match(/code:\\s*["']([^"']+)["']/);
                            const stateMatch = objTxt.match(/state:\\s*["']([^"']+)["']/);
                            const tenantMatch = objTxt.match(/tenant_url:\\s*["']([^"']+)["']/);
                            if (codeMatch && stateMatch && tenantMatch) {
                                return JSON.stringify({ code: codeMatch[1], state: stateMatch[1], tenant_url: tenantMatch[1] });
                            }
                        }
                        // 跨脚本的单字段匹配
                        const codeMatch = content.match(/["']?code["']?\\s*:\\s*["']([^"']+)["']/);
                        const stateMatch = content.match(/["']?state["']?\\s*:\\s*["']([^"']+)["']/);
                        const tenantMatch = content.match(/["']?tenant_url["']?\\s*:\\s*["']([^"']+)["']/);
                        if (codeMatch && stateMatch && tenantMatch) {
                            return JSON.stringify({ code: codeMatch[1], state: stateMatch[1], tenant_url: tenantMatch[1] });
                        }
                    }
                    return null;
                } catch { return null; }
            """)

            if auth_data_from_script:
                self.logger.capture_step(self.page, 'authorization_extracted_script', '从JavaScript提取授权码完成', logical_step=13)
                self.logger.log(f'🎯 从页面JavaScript提取授权码数据: {auth_data_from_script[:120]}...')
                return auth_data_from_script

            # 策略3: 从页面URL提取
            current_url = self.page.url
            auth_code_match = re.search(r'[?&]code=([^&]+)', current_url) or re.search(r'[?&]authorization_code=([^&]+)', current_url)
            if auth_code_match:
                auth_code = auth_code_match.group(1)
                self.logger.capture_step(self.page, 'authorization_extracted_url', '从URL提取授权码完成', logical_step=13)
                self.logger.log(f'🔗 从URL提取授权码: {auth_code}')
                return json.dumps({'code': auth_code, 'state': '', 'tenant_url': ''})

            # 策略4: 从页面内容正则提取
            page_content = self.page.html

            # 4.1 尝试匹配 let data = {...}
            auth_code_match = re.search(r'let\s+data\s*=\s*(\{[\s\S]*?\})', page_content)
            if auth_code_match:
                try:
                    # 这里需要更安全的JSON解析，而不是eval
                    obj_text = auth_code_match.group(1)
                    # 简单的字段提取
                    code_match = re.search(r'code:\s*["\']([^"\']+)["\']', obj_text)
                    state_match = re.search(r'state:\s*["\']([^"\']+)["\']', obj_text)
                    tenant_match = re.search(r'tenant_url:\s*["\']([^"\']+)["\']', obj_text)

                    if code_match and state_match and tenant_match:
                        result = {
                            'code': code_match.group(1),
                            'state': state_match.group(1),
                            'tenant_url': tenant_match.group(1)
                        }
                        self.logger.capture_step(self.page, 'authorization_extracted_content', '从页面内容提取授权码完成', logical_step=13)
                        return json.dumps(result)
                except:
                    pass

            # 4.2 直接在整页内容里提取三个字段并组装
            try:
                code_match = re.search(r'["\']?code["\']?\s*:\s*["\']([^"\']+)["\']', page_content)
                state_match = re.search(r'["\']?state["\']?\s*:\s*["\']([^"\']+)["\']', page_content)
                tenant_match = re.search(r'["\']?tenant_url["\']?\s*:\s*["\']([^"\']+)["\']', page_content)

                if code_match and state_match and tenant_match:
                    payload = {
                        'code': code_match.group(1),
                        'state': state_match.group(1),
                        'tenant_url': tenant_match.group(1)
                    }
                    self.logger.capture_step(self.page, 'authorization_extracted_content_fields', '从页面内容字段提取授权码完成')
                    self.logger.log(f'📄 从页面字段提取授权码数据: {json.dumps(payload)}')
                    return json.dumps(payload)
            except:
                pass

            raise Exception('未能提取到授权码')

        except Exception as error:
            self.logger.error('❌ 授权码提取失败', error)
            self.logger.capture_error(self.page, 'authorization_extract_failed', str(error))
            raise error

    def _create_proxy_auth_extension(self, proxy_host, proxy_port, proxy_username, proxy_password, scheme='http'):
        """创建代理认证扩展（基于网络最佳实践）"""
        import json
        import os
        import shutil
        from pathlib import Path
        from string import Template

        plugin_path = Path(__file__).parent / 'proxy_auth_extension'

        # 删除旧的扩展文件，确保使用最新配置
        if plugin_path.exists():
            shutil.rmtree(plugin_path)
            self.logger.log(f'🗑️ 已删除旧的代理扩展文件')

        plugin_path.mkdir(parents=True, exist_ok=True)
        self.logger.log(f'🔧 正在生成代理认证扩展: {proxy_username}@{proxy_host}:{proxy_port}')

        # Manifest V3 for latest Chrome versions
        manifest_json = {
            "manifest_version": 3,
            "name": "Proxy Auth Extension",
            "version": "1.0.0",
            "permissions": [
                "proxy",
                "webRequest",
                "webRequestAuthProvider"
            ],
            "host_permissions": ["<all_urls>"],
            "background": {
                "service_worker": "background.js"
            },
            "minimum_chrome_version": "88"
        }

        # Service worker for Manifest V3
        background_js = Template("""
chrome.proxy.settings.set({
    value: {
        mode: "fixed_servers",
        rules: {
            singleProxy: {
                scheme: "${scheme}",
                host: "${host}",
                port: ${port}
            },
            bypassList: ["localhost", "127.0.0.1"]
        }
    },
    scope: "regular"
});

chrome.webRequest.onAuthRequired.addListener(
    (details) => {
        return {
            authCredentials: {
                username: "${username}",
                password: "${password}"
            }
        };
    },
    {urls: ["<all_urls>"]},
    ["blocking"]
);
""").substitute(
            host=proxy_host,
            port=proxy_port,
            username=proxy_username,
            password=proxy_password,
            scheme=scheme
        )

        # Write manifest.json
        with open(plugin_path / "manifest.json", "w", encoding='utf-8') as f:
            json.dump(manifest_json, f, indent=2)

        # Write background.js
        with open(plugin_path / "background.js", "w", encoding='utf-8') as f:
            f.write(background_js)

        return str(plugin_path.resolve())

    def _activate_fingerprint_protection(self):
        """激活指纹防护（在浏览器启动后立即执行）"""
        try:
            self.logger.log('🛡️ 激活多层指纹防护系统...')

            # 1. Canvas Fingerprint Defender 插件（已在浏览器启动时加载）
            self.logger.log('✅ Canvas Fingerprint Defender 插件已加载')

            # 2. 高级 JavaScript 指纹防护
            if self.advanced_fingerprint_protection:
                protection_success = self.advanced_fingerprint_protection.inject_comprehensive_protection()
                if protection_success:
                    self.logger.log('✅ 高级 JavaScript 指纹防护已激活')

                    # 测试防护效果
                    test_results = self.advanced_fingerprint_protection.test_fingerprint_protection()
                    if test_results:
                        self.logger.log('🔍 指纹防护验证完成')
                else:
                    self.logger.log('⚠️ 高级指纹防护激活失败')

            # 3. 强化 Canvas 指纹随机化
            if self.canvas_randomizer:
                canvas_success = self.canvas_randomizer.force_canvas_randomization()
                if canvas_success:
                    self.logger.log('✅ Canvas 指纹随机化已强化')
                else:
                    self.logger.log('⚠️ Canvas 指纹随机化强化失败')

            self.logger.log('🛡️ 多层指纹防护系统已全面激活')

        except Exception as e:
            self.logger.log(f'❌ 指纹防护激活失败: {e}')

    def cleanup(self):
        """清理资源"""
        self.logger.log('🧹 清理资源...')

        try:
            if self.page:
                self.page.quit()
                self.logger.log('✅ 浏览器已关闭')
        except Exception as error:
            self.logger.error('❌ 浏览器关闭失败', error)

        # 清理虚拟显示器
        self._cleanup_virtual_display()

    def __del__(self):
        """析构函数，确保虚拟显示器被清理"""
        self._cleanup_virtual_display()
