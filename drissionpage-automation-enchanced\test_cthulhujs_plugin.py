#!/usr/bin/env python3
"""
测试 CthulhuJS Anti-Fingerprint 插件集成
"""

import time
from drissionpage import ChromiumPage, ChromiumOptions
from config import DrissionPageConfig
from drissionpage_logger import DrissionPageLogger
from cthulhujs_plugin import CthulhuJSPlugin

def test_cthulhujs_plugin():
    """测试 CthulhuJS 插件是否正常工作"""
    logger = DrissionPageLogger()
    config = DrissionPageConfig()
    
    logger.log('🧪 开始测试 CthulhuJS Anti-Fingerprint 插件...')
    
    try:
        # 创建浏览器选项
        options = ChromiumOptions()
        
        # 基础配置
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-setuid-sandbox')
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-gpu')
        options.set_argument('--window-size=1920,1080')
        
        # 如果配置为 headfull 模式
        if config.headfull:
            logger.log('🖥️ 使用有界面模式测试')
            # 移除 --disable-extensions 以支持插件
            # options.set_argument('--disable-extensions')  # 注释掉这行
        else:
            logger.log('👻 使用无界面模式测试')
            options.set_argument('--headless=new')
        
        # 加载 CthulhuJS 插件
        if config.cthulhujs_plugin:
            try:
                logger.log('🐙 正在加载 CthulhuJS Anti-Fingerprint 插件...')
                cthulhujs_manager = CthulhuJSPlugin(logger)
                
                # 设置插件
                if cthulhujs_manager.setup_plugin_directory():
                    # 验证插件
                    is_valid, message = cthulhujs_manager.validate_plugin()
                    if is_valid:
                        plugin_path = cthulhujs_manager.get_plugin_path()
                        options.add_extension(plugin_path)
                        logger.log(f'🧩 已加载 CthulhuJS Anti-Fingerprint 插件: {plugin_path}')
                    else:
                        logger.log(f'⚠️ CthulhuJS 插件验证失败: {message}')
                else:
                    logger.log('⚠️ CthulhuJS 插件设置失败')
                    
            except Exception as e:
                logger.log(f'⚠️ CthulhuJS 插件加载失败: {e}')
        else:
            logger.log('⚠️ CthulhuJS 插件已禁用')
        
        # 创建页面
        logger.log('🚀 启动浏览器...')
        page = ChromiumPage(addr_or_opts=options)
        
        logger.log('✅ 浏览器启动成功')
        
        # 测试指纹防护效果
        logger.log('🧪 测试指纹防护效果...')
        
        # 导航到指纹测试页面
        test_url = 'https://browserleaks.com/canvas'
        logger.log(f'🌐 导航到测试页面: {test_url}')
        page.get(test_url)
        
        # 等待页面加载
        time.sleep(3)
        
        # 检查页面标题
        title = page.title
        logger.log(f'📄 页面标题: {title}')
        
        # 测试 Canvas 指纹
        logger.log('🎨 测试 Canvas 指纹...')
        canvas_result = page.run_js('''
            // 创建 canvas 元素
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // 绘制测试图案
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('CthulhuJS Test 🐙', 2, 2);
            
            // 获取 canvas 数据
            return canvas.toDataURL();
        ''')
        
        if canvas_result:
            logger.log(f'🎨 Canvas 指纹 (前50字符): {canvas_result[:50]}...')
        else:
            logger.log('⚠️ Canvas 指纹获取失败')
        
        # 测试 WebGL 指纹
        logger.log('🔍 测试 WebGL 指纹...')
        webgl_result = page.run_js('''
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (gl) {
                    const renderer = gl.getParameter(gl.RENDERER);
                    const vendor = gl.getParameter(gl.VENDOR);
                    return {
                        renderer: renderer,
                        vendor: vendor
                    };
                } else {
                    return null;
                }
            } catch (e) {
                return { error: e.message };
            }
        ''')
        
        if webgl_result:
            logger.log(f'🔍 WebGL 渲染器: {webgl_result.get("renderer", "未知")}')
            logger.log(f'🔍 WebGL 供应商: {webgl_result.get("vendor", "未知")}')
        else:
            logger.log('⚠️ WebGL 信息获取失败')
        
        # 测试用户代理
        user_agent = page.run_js('return navigator.userAgent;')
        logger.log(f'🤖 用户代理: {user_agent}')
        
        # 测试平台信息
        platform_info = page.run_js('''
            return {
                platform: navigator.platform,
                language: navigator.language,
                hardwareConcurrency: navigator.hardwareConcurrency,
                cookieEnabled: navigator.cookieEnabled
            };
        ''')
        
        if platform_info:
            logger.log(f'💻 平台: {platform_info.get("platform", "未知")}')
            logger.log(f'🌐 语言: {platform_info.get("language", "未知")}')
            logger.log(f'⚙️ 硬件并发: {platform_info.get("hardwareConcurrency", "未知")}')
            logger.log(f'🍪 Cookie启用: {platform_info.get("cookieEnabled", "未知")}')
        
        # 检查是否有 CthulhuJS 相关的控制台消息
        logger.log('📋 检查控制台消息...')
        
        # 等待一段时间让插件完全加载
        time.sleep(2)
        
        logger.log('✅ CthulhuJS 插件测试完成')
        
        # 保持浏览器打开一段时间以便观察
        if config.headfull:
            logger.log('🔍 浏览器将保持打开10秒以便观察...')
            time.sleep(10)
        
        # 关闭浏览器
        page.quit()
        logger.log('✅ 浏览器已关闭')
        
        return True
        
    except Exception as e:
        logger.log(f'❌ 测试失败: {e}')
        return False

if __name__ == '__main__':
    success = test_cthulhujs_plugin()
    if success:
        print('🎉 CthulhuJS Anti-Fingerprint 插件测试成功！')
    else:
        print('❌ CthulhuJS Anti-Fingerprint 插件测试失败！')
